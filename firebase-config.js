// Firebase Configuration
// Replace these values with your actual Firebase project configuration
const firebaseConfig = {
    apiKey: "AIzaSyBdyL8uJAVuCFdQBvTCoBL54zw-M8Z8svk",
    authDomain: "paradox-gpt.firebaseapp.com",
    projectId: "paradox-gpt",
    storageBucket: "paradox-gpt.firebasestorage.app",
    messagingSenderId: "123169164161",
    appId: "1:123169164161:web:98a51619d63d4a7e847680",
    measurementId: "G-Z8YDCE0F6Y"
  };

// Initialize Firebase
import { initializeApp } from 'firebase/app';
import { getAuth, GoogleAuthProvider } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';

const app = initializeApp(firebaseConfig);

// Initialize Firebase Authentication and get a reference to the service
export const auth = getAuth(app);
export const googleProvider = new GoogleAuthProvider();

// Initialize Cloud Firestore and get a reference to the service
export const db = getFirestore(app);

export default app;
