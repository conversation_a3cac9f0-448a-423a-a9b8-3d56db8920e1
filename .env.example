# ParadoxGPT Environment Configuration

# =============================================================================
# REQUIRED CONFIGURATION
# =============================================================================

# API Keys (Required)
GEMINI_API_KEY=your_gemini_api_key_here

# =============================================================================
# FIREBASE CONFIGURATION (Choose ONE method)
# =============================================================================

# METHOD 1: Individual Environment Variables (RECOMMENDED for Production)
# Most secure - each credential is separate
FIREBASE_TYPE=service_account
FIREBASE_PROJECT_ID=paradox-gpt
FIREBASE_PRIVATE_KEY_ID="6793ece77b36803a2559e2f340c20c3e817ef61a"
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
FIREBASE_CLIENT_EMAIL="<EMAIL>"
FIREBASE_CLIENT_ID= "103136264274029588971"
FIREBASE_AUTH_URI= "https://accounts.google.com/o/oauth2/auth"
FIREBASE_TOKEN_URI= "https://oauth2.googleapis.com/token"
FIREBASE_AUTH_PROVIDER_X509_CERT_URL= "https://www.googleapis.com/oauth2/v1/certs"
FIREBASE_CLIENT_X509_CERT_URL= "https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-fbsvc%40paradox-gpt.iam.gserviceaccount.com"
"universe_domain": "googleapis.com"

# METHOD 2: Base64 Encoded Service Account (Alternative for Production)
# Run: base64 -w 0 /path/to/service-account.json
# FIREBASE_SERVICE_ACCOUNT_BASE64=ewogICJ0eXBlIjogInNlcnZpY2VfYWNjb3VudCIsC...

# METHOD 3: Service Account Key File Path (Development Only)
# FIREBASE_SERVICE_ACCOUNT_KEY=/path/to/your/firebase-service-account-key.json

# METHOD 4: Direct JSON String (Not Recommended)
# FIREBASE_SERVICE_ACCOUNT_JSON={"type":"service_account","project_id":"paradox-gpt",...}

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================

# Flask Environment
FLASK_ENV=production
FLASK_DEBUG=false

# Server Settings
PORT=8080
HOST=0.0.0.0

# Security Settings
SECRET_KEY=your_secret_key_here_change_in_production

# =============================================================================
# OPTIONAL CONFIGURATION
# =============================================================================

# Logging Level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# Firebase Debug Mode
FIREBASE_DEBUG=false

# Rate Limiting (requests per minute)
RATE_LIMIT=60

# Session Timeout (in seconds)
SESSION_TIMEOUT=3600

# =============================================================================
# DEPLOYMENT SPECIFIC
# =============================================================================

# For Docker deployments
# PYTHONPATH=/app
# PYTHONUNBUFFERED=1

# For Google Cloud Run
# GOOGLE_CLOUD_PROJECT=paradox-gpt
# GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account.json

# For Heroku
# DYNO=web.1

# For AWS
# AWS_DEFAULT_REGION=us-east-1
