# ParadoxGPT Environment Configuration

# API Keys (Required)
GEMINI_API_KEY=your_gemini_api_key_here

# Firebase Configuration (Optional - for user authentication and chat storage)
# Option 1: Path to service account key file
FIREBASE_SERVICE_ACCOUNT_KEY=/path/to/your/firebase-service-account-key.json

# Option 2: Service account JSON as environment variable (recommended for production)
# FIREBASE_SERVICE_ACCOUNT_JSON={"type": "service_account", "project_id": "your-project-id", ...}

# Debug Settings
FIREBASE_DEBUG=false

# Server Configuration
PORT=5000
HOST=127.0.0.1
DEBUG=false
